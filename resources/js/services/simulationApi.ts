export const simulationApi = {
    async playAllWeeks(seasonId: string) {
        const response = await fetch('/play-all-weeks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            },
            body: JSON.stringify({ season_id: seasonId }),
        });

        if (!response.ok) {
            throw new Error('Failed to play all weeks');
        }

        return await response.json();
    },

    async playNextWeek(seasonId: string) {
        const response = await fetch('/play-next-week', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            },
            body: JSON.stringify({ season_id: seasonId }),
        });

        if (!response.ok) {
            throw new Error('Failed to play next week');
        }

        return await response.json();
    }
};
