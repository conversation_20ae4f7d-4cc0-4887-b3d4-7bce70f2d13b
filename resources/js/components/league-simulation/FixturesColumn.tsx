import { useState } from 'react';
import { Play, FastForward } from 'lucide-react';
import { Match, SimulationResult } from '@/types/simulation';
import { simulationApi } from '@/services/simulationApi';

interface FixturesColumnProps {
    fixturesByWeek: Record<string, Match[]>;
    buttonsDisabled: boolean;
    seasonId: string;
    onFixturesUpdate: (data: SimulationResult) => void;
}

export default function FixturesColumn({
    fixturesByWeek,
    buttonsDisabled,
    seasonId,
    onFixturesUpdate
}: FixturesColumnProps) {
    const [isLoading, setIsLoading] = useState(false);
    const weekNumbers = Object.keys(fixturesByWeek);

    const handlePlayAllWeeks = async () => {
        if (buttonsDisabled || isLoading) return;

        setIsLoading(true);
        try {
            const data = await simulationApi.playAllWeeks(seasonId);
            onFixturesUpdate(data);
        } catch (error) {
            console.error('Error playing all weeks:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handlePlayNextWeek = async () => {
        if (buttonsDisabled || isLoading) return;

        setIsLoading(true);
        try {
            const data = await simulationApi.playNextWeek(seasonId);
            onFixturesUpdate(data);
        } catch (error) {
            console.error('Error playing next week:', error);
        } finally {
            setIsLoading(false);
        }
    };
    return (
        <div className="lg:col-span-1">
            <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                    <h2 className="text-lg font-medium text-gray-900">Fixtures</h2>
                    <div className="flex space-x-2">
                        <button
                            onClick={handlePlayNextWeek}
                            disabled={buttonsDisabled || isLoading}
                            className={`flex items-center space-x-1 px-3 py-1 text-sm rounded ${
                                buttonsDisabled || isLoading
                                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                    : 'bg-blue-600 text-white hover:bg-blue-700'
                            }`}
                        >
                            <Play size={14} />
                            <span>{isLoading ? 'Playing...' : 'Play Next Week'}</span>
                        </button>
                        <button
                            onClick={handlePlayAllWeeks}
                            disabled={buttonsDisabled || isLoading}
                            className={`flex items-center space-x-1 px-3 py-1 text-sm rounded ${
                                buttonsDisabled || isLoading
                                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                    : 'bg-green-600 text-white hover:bg-green-700'
                            }`}
                        >
                            <FastForward size={14} />
                            <span>{isLoading ? 'Playing...' : 'Play All Weeks'}</span>
                        </button>
                    </div>
                </div>
                <div className="p-4 max-h-96 overflow-y-auto">
                    {weekNumbers.map((weekNumber) => (
                        <div key={weekNumber} className="mb-4">
                            <h3 className="text-sm font-medium text-gray-900 mb-2">
                                Week {weekNumber}
                            </h3>
                            <div className="space-y-2">
                                {fixturesByWeek[weekNumber].map((match, index) => (
                                    <div
                                        key={index}
                                        className="bg-gray-50 rounded p-2 text-sm"
                                    >
                                        <div className="flex items-center justify-between">
                                            <span className="text-gray-900">{match.home_team.name}</span>
                                            <div className="px-2">
                                                {match.is_played ? (
                                                    <span className="font-bold text-gray-900">
                                                        {match.home_score} - {match.away_score}
                                                    </span>
                                                ) : (
                                                    <span className="text-gray-400">vs</span>
                                                )}
                                            </div>
                                            <span className="text-gray-900">{match.away_team.name}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}
