interface PredictionsColumnProps {
    predictions: Record<string, number>;
}

export default function PredictionsColumn({ predictions }: PredictionsColumnProps) {
    const hasPredictions = predictions && Object.keys(predictions).length > 0;

    return (
        <div className="lg:col-span-1">
            <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-3 border-b border-gray-200">
                    <h2 className="text-lg font-medium text-gray-900">Championship Predictions</h2>
                </div>
                <div className="p-4">
                    {hasPredictions ? (
                        <div className="space-y-3">
                            {Object.entries(predictions)
                                .sort(([,a], [,b]) => b - a)
                                .map(([team, percentage]) => (
                                <div key={team} className="flex items-center justify-between">
                                    <span className="text-sm text-gray-900">{team}</span>
                                    <div className="flex items-center">
                                        <div className="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                            <div
                                                className="bg-blue-600 h-2 rounded-full"
                                                style={{ width: `${percentage}%` }}
                                            ></div>
                                        </div>
                                        <span className="text-sm font-medium text-gray-900 w-8">
                                            {percentage}%
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center text-gray-500 py-8">
                            <p className="text-sm">Championship predictions will be available after week 3.</p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
