<?php

use App\Http\Controllers\LeagueController;
use Illuminate\Support\Facades\Route;

Route::get('/', [LeagueController::class, 'getTeams'])->name('home');
Route::get('/start-simulation', [LeagueController::class, 'generateFixture'])->name('start-simulation');
Route::get('/simulation', [LeagueController::class, 'simulationIndex'])->name('simulation');
Route::post('/play-all-weeks', [LeagueController::class, 'playAllWeeks'])->name('play-all-weeks');
Route::post('/play-next-week', [LeagueController::class, 'playNextWeek'])->name('play-next-week');
