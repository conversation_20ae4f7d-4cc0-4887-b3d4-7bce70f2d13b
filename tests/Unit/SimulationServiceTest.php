<?php

namespace Tests\Unit;

use App\Models\Team;
use App\Services\FixtureGenerator;
use App\Services\SimulationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SimulationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected SimulationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new SimulationService();
    }

    public function test_simulate_match_result_returns_valid_scores()
    {
        $result = $this->callPrivateMethod('simulateMatchResult', [70, 30]);

        $this->assertArrayHasKey('home_score', $result);
        $this->assertArrayHasKey('away_score', $result);
        $this->assertIsInt($result['home_score']);
        $this->assertIsInt($result['away_score']);
        $this->assertGreaterThanOrEqual(0, $result['home_score']);
        $this->assertGreaterThanOrEqual(0, $result['away_score']);
    }

    public function test_calculate_championship_predictions()
    {
        $standings = [
            ['team' => 'A', 'points' => 12],
            ['team' => 'B', 'points' => 9],
            ['team' => 'C', 'points' => 6],
            ['team' => 'D', 'points' => 3],
        ];

        $predictions = $this->service->calculateChampionshipPredictions($standings, 4);

        $this->assertIsArray($predictions);
        $this->assertArrayHasKey('A', $predictions);
        $this->assertGreaterThan($predictions['A'], $predictions['D']);
    }

    public function test_get_simulation_result_returns_dto()
    {
        $teams = Team::factory()->count(4)->create();
        $seasonId = 'SEAS1234';

        app(FixtureGenerator::class)->generate($teams->pluck('id')->toArray(), $seasonId);

        $result = $this->service->getSimulationResult($seasonId);

        $this->assertNotEmpty($result->fixtures);
        $this->assertIsArray($result->standings);
        $this->assertIsInt($result->currentWeek);
    }

    private function callPrivateMethod(string $method, array $args)
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod($method);
        $method->setAccessible(true);
        return $method->invokeArgs($this->service, $args);
    }
}
