<?php

namespace Database\Factories;

use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Team>
 */
class TeamFactory extends Factory
{
    protected $model = Team::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->company() . ' FC',
        ];
    }

    /**
     * Create teams with specific names
     */
    public function withName(string $name): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $name,
        ]);
    }

    /**
     * Create a set of Premier League teams for testing
     */
    public function premierLeague(): static
    {
        static $teams = [
            'Arsenal', 'Chelsea', 'Liverpool', 'Manchester United',
            'Manchester City', 'Tottenham', 'Newcastle', 'Brighton'
        ];
        
        static $index = 0;
        $teamName = $teams[$index % count($teams)];
        $index++;
        
        return $this->withName($teamName);
    }
}
