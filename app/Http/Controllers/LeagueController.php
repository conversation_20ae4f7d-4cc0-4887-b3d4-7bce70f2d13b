<?php

namespace App\Http\Controllers;

use App\Models\LeagueMatches;
use App\Models\Team;
use App\Services\FixtureGenerator;
use App\Services\SimulationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class LeagueController extends Controller
{
    protected SimulationService $simulationService;

    public function __construct(SimulationService $simulationService)
    {
        $this->simulationService = $simulationService;
    }

    /**
     * @return Response
     */
    public function getTeams()
    {
        $teams = Team::all();

        return Inertia::render('Teams', [
            'teams' => $teams
        ]);
    }

    /**
     * @param FixtureGenerator $generator
     * @return RedirectResponse
     */
    public function generateFixture(FixtureGenerator $generator): RedirectResponse
    {
        $teamIds = Team::pluck('id')->toArray();

        $seasonId = Str::random(8);
        $generator->generate($teamIds, $seasonId);

        return redirect()
            ->route('simulation')
            ->cookie('simulation_season_id', $seasonId, 60);
    }


    /**
     * @param Request $request
     * @return Response|RedirectResponse
     */
    public function simulationIndex(Request $request): Response|RedirectResponse
    {
        $cookieSeasonId = $request->cookie('simulation_season_id');
        $season = LeagueMatches::where('season_id', $cookieSeasonId)->exists();

        if ($season) {
            $seasonId = $cookieSeasonId;
        } else {
            return redirect()->route('home');
        }

        $simulationResult = $this->simulationService->getSimulationResult($seasonId);

        $simulationData = $simulationResult->toArray();

        return Inertia::render('Simulation', $simulationData);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function playAllWeeks(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $matches = LeagueMatches::where('is_played', false)
            ->where('season_id', $seasonId)
            ->get();

        foreach ($matches as $match) {
            $this->simulationService->simulateMatchAsPlayed($match);
        }

        $simulationResult = $this->simulationService->getSimulationResult($seasonId);

        return response()->json($simulationResult->toArray());
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function playNextWeek(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $nextWeek = LeagueMatches::where('season_id', $seasonId)
            ->where('is_played', false)
            ->min('week');

        if (is_null($nextWeek)) {
            return response()->json([
                'message' => 'All matches have already been played for this season.'
            ]);
        }

        $unplayedMatches = LeagueMatches::where('season_id', $seasonId)
            ->where('week', $nextWeek)
            ->where('is_played', false)
            ->get();

        foreach ($unplayedMatches as $match) {
            $this->simulationService->simulateMatchAsPlayed($match);
        }

        $simulationResult = $this->simulationService->getSimulationResult($seasonId);

        $responseData = $simulationResult->toArray();
        $responseData['week'] = $nextWeek;

        return response()->json($responseData);
    }

}
