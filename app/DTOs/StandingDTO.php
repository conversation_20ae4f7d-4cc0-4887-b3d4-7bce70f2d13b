<?php

namespace App\DTOs;

class StandingDTO
{
    public function __construct(
        public string $team,
        public int $played = 0,
        public int $won = 0,
        public int $drawn = 0,
        public int $lost = 0,
        public int $goalsFor = 0,
        public int $goalsAgainst = 0,
        public int $points = 0
    ) {}

    public function toArray(): array
    {
        return [
            'team' => $this->team,
            'played' => $this->played,
            'won' => $this->won,
            'drawn' => $this->drawn,
            'lost' => $this->lost,
            'points' => $this->points,
        ];
    }

    public function addWin(): void
    {
        $this->played++;
        $this->won++;
        $this->points += 3;
    }

    public function addDraw(): void
    {
        $this->played++;
        $this->drawn++;
        $this->points += 1;
    }

    public function addLoss(): void
    {
        $this->played++;
        $this->lost++;
    }
}
