<?php

namespace App\Services;

use App\Models\LeagueMatches;

class FixtureGenerator
{
    public function generate(array $teamIds, string $seasonId): array
    {
        // First Leg Matches
        $firstLegMatches = $this->generateFirstLegMatches($teamIds);
        $firstLegFixture = $this->createFixtureWithWeeks($firstLegMatches, 1, $seasonId);

        // Second Leg Matches / Revenges
        $secondLegMatches = $this->generateSecondLegMatches($firstLegMatches);
        $firstLegLastWeek = $this->getLastWeekNumber($firstLegFixture);
        $secondLegFixture = $this->createFixtureWithWeeks($secondLegMatches, $firstLegLastWeek + 1, $seasonId);

        return array_merge($firstLegFixture, $secondLegFixture);
    }

    private function generateFirstLegMatches(array $teams): array
    {
        $matches = [];

        foreach ($teams as $homeTeamIndex => $homeTeamId) {
            foreach ($teams as $awayTeamIndex => $awayTeamId) {
                if ($homeTeamIndex < $awayTeamIndex) {
                    $matches[] = LeagueMatches::make([
                            'home_team_id' => $homeTeamId,
                            'away_team_id' => $awayTeamId,
                        ]);
                }
            }
        }

        shuffle($matches);

        return $matches;
    }

    private function generateSecondLegMatches(array $firstLegMatches): array
    {
        $secondLeg = [];

        foreach ($firstLegMatches as $match) {
            $secondLeg[] = LeagueMatches::make([
                'home_team_id' => $match->away_team_id,
                'away_team_id' => $match->home_team_id,
            ]);
        }

        shuffle($secondLeg);

        return $secondLeg;
    }

    private function createFixtureWithWeeks(array $matches, int $startingWeek, string $seasonId): array
    {
        $fixture = [];
        $week = $startingWeek;
        $remaining = $matches;

        while (count($remaining) > 0) {
            $weekMatches = [];
            $teamsScheduled = [];

            foreach ($remaining as $key => $match) {
                $homeId = $match->home_team_id;
                $awayId = $match->away_team_id;

                if (!in_array($homeId, $teamsScheduled) && !in_array($awayId, $teamsScheduled)) {
                    $match->season_id = $seasonId;
                    $match->week = $week;
                    $weekMatches[] = $match;
                    $teamsScheduled[] = $homeId;
                    $teamsScheduled[] = $awayId;
                    $match->save();
                    unset($remaining[$key]);
                }
            }

            $fixture = array_merge($fixture, $weekMatches);
            $week++;
        }

        return $fixture;
    }


    private function getLastWeekNumber(array $fixture): int
    {
        return collect($fixture)->max('week') ?? 0;
    }
}
